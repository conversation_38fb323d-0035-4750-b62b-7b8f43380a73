const config = {
  host: process.env.POSTGRES_HOST,
  username: process.env.POSTGRES_USER,
  database: process.env.POSTGRES_DB,
  password: process.env.POSTGRES_PASSWORD,
  port: process.env.POSTGRES_PORT,
  dialect: 'postgres',
  logging: false,
  pool: {
    max: 20,       // Increased from 5 to 20 for better concurrency
    min: 2,        // Keep at least 2 connections alive
    acquire: 30000, // Maximum time to get connection before throwing error
    idle: 10000,   // Close idle connections after 10 seconds
    evict: 5000,   // Check for idle connections every 5 seconds
  },
};
module.exports = {
  development: config,
  test: config,
  testing: config,
  staging: config,
  production: config,
  prod: config,
};
